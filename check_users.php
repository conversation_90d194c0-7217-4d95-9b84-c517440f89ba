<?php
// <PERSON>ript để kiểm tra thông tin user trong database
require_once('config.php');

// Kết nối database
$host = $incomCRM_config['dbconfig']['db_host_name'];
$username = $incomCRM_config['dbconfig']['db_user_name'];
$password = $incomCRM_config['dbconfig']['db_password'];
$database = $incomCRM_config['dbconfig']['db_name'];

try {
    $pdo = new PDO("mysql:host=$host;dbname=$database", $username, $password);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    echo "Kết nối database thành công!\n\n";
    
    // Lấy danh sách user admin
    $sql = "SELECT user_name, first_name, last_name, status, is_admin, date_entered 
            FROM users 
            WHERE status='Active' AND deleted=0 
            ORDER BY is_admin DESC, date_entered ASC 
            LIMIT 10";
    
    $stmt = $pdo->query($sql);
    $users = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    echo "Danh sách users trong hệ thống:\n";
    echo "================================\n";
    
    foreach ($users as $user) {
        echo "Username: " . $user['user_name'] . "\n";
        echo "Tên: " . $user['first_name'] . " " . $user['last_name'] . "\n";
        echo "Status: " . $user['status'] . "\n";
        echo "Admin: " . ($user['is_admin'] ? 'Yes' : 'No') . "\n";
        echo "Ngày tạo: " . $user['date_entered'] . "\n";
        echo "--------------------------------\n";
    }
    
    // Kiểm tra user admin đầu tiên
    $sql_admin = "SELECT user_name FROM users WHERE status='Active' AND deleted=0 AND is_admin=1 ORDER BY date_entered ASC LIMIT 1";
    $stmt_admin = $pdo->query($sql_admin);
    $admin_user = $stmt_admin->fetch(PDO::FETCH_ASSOC);
    
    if ($admin_user) {
        echo "\nTài khoản admin đầu tiên: " . $admin_user['user_name'] . "\n";
        echo "Thử đăng nhập với username: " . $admin_user['user_name'] . "\n";
        echo "Password thường là: admin hoặc 123456 hoặc password\n";
    }
    
} catch(PDOException $e) {
    echo "Lỗi kết nối database: " . $e->getMessage() . "\n";
    
    // Thử với root user không password
    try {
        echo "\nThử kết nối với root user...\n";
        $pdo_root = new PDO("mysql:host=$host;dbname=$database", 'root', '');
        $pdo_root->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
        
        echo "Kết nối với root thành công!\n\n";
        
        $sql = "SELECT user_name, first_name, last_name, status, is_admin 
                FROM users 
                WHERE status='Active' AND deleted=0 
                ORDER BY is_admin DESC, date_entered ASC 
                LIMIT 5";
        
        $stmt = $pdo_root->query($sql);
        $users = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        echo "Danh sách users:\n";
        foreach ($users as $user) {
            echo "- Username: " . $user['user_name'] . " (Admin: " . ($user['is_admin'] ? 'Yes' : 'No') . ")\n";
        }
        
    } catch(PDOException $e2) {
        echo "Lỗi kết nối với root: " . $e2->getMessage() . "\n";
    }
}
?>
